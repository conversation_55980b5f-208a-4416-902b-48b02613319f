"""
Telegram Conversation Manager for WhatsApp AI Assistant.
Manages conversational AI interactions through Telegram with access to WhatsApp data.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import json

from utils.logging import get_logger
from utils.config import get_config
from data.database import get_database_manager
from data.models import Contact, Message
from sqlalchemy import text, desc

logger = get_logger("telegram_conversation")


class TelegramConversationManager:
    """Manages conversational AI interactions through Telegram."""
    
    def __init__(self, ai_engine=None):
        self.config = get_config()
        self.db_manager = get_database_manager()
        self.ai_engine = ai_engine
        
        # Conversation memory - stores context for each user
        self.conversation_memory: Dict[int, List[Dict[str, Any]]] = {}
        self.max_memory_messages = 20  # Keep last 20 messages in memory
        self.memory_cleanup_interval = 3600  # Clean up old conversations every hour
        
        # WhatsApp data cache
        self.contacts_cache: Dict[str, Any] = {}
        self.cache_expiry = 300  # 5 minutes
        self.last_cache_update = None
        
        logger.info("Telegram conversation manager initialized")
    
    async def initialize(self) -> bool:
        """Initialize the conversation manager."""
        try:
            # Load contacts cache
            await self._update_contacts_cache()
            
            # Start memory cleanup task
            asyncio.create_task(self._memory_cleanup_task())
            
            logger.info("Telegram conversation manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize conversation manager: {e}")
            return False
    
    async def process_message(self, user_id: int, message_text: str) -> str:
        """Process a conversational message from Telegram user."""
        try:
            # Add message to conversation memory
            await self._add_to_memory(user_id, "user", message_text)
            
            # Get conversation context
            context = await self._build_conversation_context(user_id, message_text)
            
            # Generate AI response
            response = await self._generate_ai_response(context)
            
            # Add response to memory
            await self._add_to_memory(user_id, "assistant", response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return "❌ Sorry, I encountered an error processing your message. Please try again."
    
    async def _add_to_memory(self, user_id: int, role: str, content: str):
        """Add a message to conversation memory."""
        if user_id not in self.conversation_memory:
            self.conversation_memory[user_id] = []
        
        memory_entry = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        
        self.conversation_memory[user_id].append(memory_entry)
        
        # Keep only the last N messages
        if len(self.conversation_memory[user_id]) > self.max_memory_messages:
            self.conversation_memory[user_id] = self.conversation_memory[user_id][-self.max_memory_messages:]
    
    async def _build_conversation_context(self, user_id: int, current_message: str) -> Dict[str, Any]:
        """Build comprehensive context for AI response generation."""
        context = {
            "current_message": current_message,
            "conversation_history": self.conversation_memory.get(user_id, []),
            "whatsapp_data": await self._get_relevant_whatsapp_data(current_message),
            "system_info": await self._get_system_info(),
            "timestamp": datetime.now().isoformat()
        }
        
        return context
    
    async def _get_relevant_whatsapp_data(self, message: str) -> Dict[str, Any]:
        """Get relevant WhatsApp data based on the message content."""
        try:
            # Update contacts cache if needed
            await self._update_contacts_cache()
            
            whatsapp_data = {
                "total_contacts": len(self.contacts_cache),
                "recent_messages": await self._get_recent_messages(limit=10),
                "active_conversations": await self._get_active_conversations(),
                "message_stats": await self._get_message_statistics()
            }
            
            # If message mentions specific contacts, get their data
            mentioned_contacts = await self._find_mentioned_contacts(message)
            if mentioned_contacts:
                whatsapp_data["mentioned_contacts"] = mentioned_contacts
                whatsapp_data["contact_conversations"] = await self._get_contact_conversations(mentioned_contacts)
            
            # If message asks about specific topics, search for relevant messages
            if any(keyword in message.lower() for keyword in ["search", "find", "show", "what", "when", "who"]):
                whatsapp_data["relevant_messages"] = await self._search_messages(message)
            
            return whatsapp_data
            
        except Exception as e:
            logger.error(f"Error getting WhatsApp data: {e}")
            return {}
    
    async def _update_contacts_cache(self):
        """Update the contacts cache."""
        try:
            current_time = datetime.now()
            if (self.last_cache_update and 
                current_time - self.last_cache_update < timedelta(seconds=self.cache_expiry)):
                return
            
            with self.db_manager.get_session() as session:
                contacts = session.query(Contact).all()
                
                self.contacts_cache = {}
                for contact in contacts:
                    self.contacts_cache[contact.phone_number] = {
                        "id": contact.id,
                        "name": contact.name,
                        "phone_number": contact.phone_number,
                        "category": contact.category,
                        "last_seen": contact.last_seen.isoformat() if contact.last_seen else None
                    }
            
            self.last_cache_update = current_time
            logger.debug(f"Updated contacts cache with {len(self.contacts_cache)} contacts")
            
        except Exception as e:
            logger.error(f"Error updating contacts cache: {e}")
    
    async def _get_recent_messages(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent WhatsApp messages."""
        try:
            with self.db_manager.get_session() as session:
                messages = session.query(Message).join(Contact).order_by(
                    desc(Message.timestamp)
                ).limit(limit).all()
                
                recent_messages = []
                for msg in messages:
                    recent_messages.append({
                        "id": msg.id,
                        "contact_name": msg.contact.name if msg.contact else "Unknown",
                        "message_text": msg.message_text[:100] + "..." if len(msg.message_text) > 100 else msg.message_text,
                        "timestamp": msg.timestamp.isoformat() if msg.timestamp else None,
                        "is_from_me": msg.is_from_me
                    })
                
                return recent_messages
                
        except Exception as e:
            logger.error(f"Error getting recent messages: {e}")
            return []
    
    async def _get_active_conversations(self) -> List[Dict[str, Any]]:
        """Get active conversations (contacts with recent messages)."""
        try:
            with self.db_manager.get_session() as session:
                # Get contacts with messages in the last 7 days
                result = session.execute(text("""
                    SELECT c.id, c.name, c.phone_number, 
                           COUNT(m.id) as message_count,
                           MAX(m.timestamp) as last_message
                    FROM contacts c
                    JOIN messages m ON c.id = m.contact_id
                    WHERE m.timestamp > datetime('now', '-7 days')
                    GROUP BY c.id, c.name, c.phone_number
                    ORDER BY last_message DESC
                    LIMIT 10
                """))
                
                conversations = []
                for row in result:
                    conversations.append({
                        "contact_id": row[0],
                        "contact_name": row[1],
                        "phone_number": row[2],
                        "message_count": row[3],
                        "last_message": row[4]
                    })
                
                return conversations
                
        except Exception as e:
            logger.error(f"Error getting active conversations: {e}")
            return []
    
    async def _get_message_statistics(self) -> Dict[str, Any]:
        """Get general message statistics."""
        try:
            with self.db_manager.get_session() as session:
                # Total messages
                result = session.execute(text("SELECT COUNT(*) FROM messages"))
                total_messages = result.scalar()
                
                # Messages today
                result = session.execute(text("""
                    SELECT COUNT(*) FROM messages 
                    WHERE DATE(timestamp) = DATE('now')
                """))
                messages_today = result.scalar()
                
                # Messages this week
                result = session.execute(text("""
                    SELECT COUNT(*) FROM messages 
                    WHERE timestamp > datetime('now', '-7 days')
                """))
                messages_week = result.scalar()
                
                # Unique contacts
                result = session.execute(text("SELECT COUNT(DISTINCT contact_id) FROM messages"))
                unique_contacts = result.scalar()
                
                return {
                    "total_messages": total_messages,
                    "messages_today": messages_today,
                    "messages_this_week": messages_week,
                    "unique_contacts": unique_contacts
                }
                
        except Exception as e:
            logger.error(f"Error getting message statistics: {e}")
            return {}
    
    async def _find_mentioned_contacts(self, message: str) -> List[Dict[str, Any]]:
        """Find contacts mentioned in the message."""
        try:
            mentioned_contacts = []
            message_lower = message.lower()
            
            for phone, contact_data in self.contacts_cache.items():
                if contact_data["name"]:
                    name_lower = contact_data["name"].lower()
                    # Check if contact name is mentioned
                    if name_lower in message_lower:
                        mentioned_contacts.append(contact_data)
            
            return mentioned_contacts
            
        except Exception as e:
            logger.error(f"Error finding mentioned contacts: {e}")
            return []
    
    async def _get_contact_conversations(self, contacts: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Get recent conversations for specific contacts."""
        try:
            conversations = {}
            
            with self.db_manager.get_session() as session:
                for contact in contacts:
                    contact_id = contact["id"]
                    
                    messages = session.query(Message).filter_by(
                        contact_id=contact_id
                    ).order_by(desc(Message.timestamp)).limit(5).all()
                    
                    contact_messages = []
                    for msg in messages:
                        contact_messages.append({
                            "message_text": msg.message_text,
                            "timestamp": msg.timestamp.isoformat() if msg.timestamp else None,
                            "is_from_me": msg.is_from_me
                        })
                    
                    conversations[contact["name"]] = contact_messages
            
            return conversations
            
        except Exception as e:
            logger.error(f"Error getting contact conversations: {e}")
            return {}
    
    async def _search_messages(self, query: str) -> List[Dict[str, Any]]:
        """Search for messages based on query."""
        try:
            # Simple keyword search for now
            # In the future, this could use the vector database for semantic search
            keywords = [word.lower() for word in query.split() if len(word) > 3]
            
            if not keywords:
                return []
            
            with self.db_manager.get_session() as session:
                # Build search query
                search_conditions = []
                for keyword in keywords[:3]:  # Limit to 3 keywords
                    search_conditions.append(f"LOWER(m.message_text) LIKE '%{keyword}%'")
                
                if not search_conditions:
                    return []
                
                search_query = f"""
                    SELECT m.message_text, m.timestamp, m.is_from_me, c.name as contact_name
                    FROM messages m
                    JOIN contacts c ON m.contact_id = c.id
                    WHERE ({' OR '.join(search_conditions)})
                    ORDER BY m.timestamp DESC
                    LIMIT 5
                """
                
                result = session.execute(text(search_query))
                
                search_results = []
                for row in result:
                    search_results.append({
                        "message_text": row[0][:200] + "..." if len(row[0]) > 200 else row[0],
                        "timestamp": row[1],
                        "is_from_me": row[2],
                        "contact_name": row[3]
                    })
                
                return search_results
                
        except Exception as e:
            logger.error(f"Error searching messages: {e}")
            return []
    
    async def _get_system_info(self) -> Dict[str, Any]:
        """Get current system information."""
        return {
            "current_time": datetime.now().isoformat(),
            "assistant_name": "WhatsApp AI Assistant",
            "capabilities": [
                "Access WhatsApp message history",
                "Search conversations",
                "Provide contact information",
                "Generate conversation summaries",
                "Answer questions about message patterns"
            ]
        }
    
    async def _generate_ai_response(self, context: Dict[str, Any]) -> str:
        """Generate AI response using the AI engine."""
        try:
            # Create a comprehensive prompt for the AI
            prompt = await self._build_ai_prompt(context)

            # Try to use the AI engine if available
            if self.ai_engine:
                try:
                    # Use the AI engine to generate a response
                    ai_response = await self._use_ai_engine(prompt, context)
                    if ai_response:
                        return ai_response
                except Exception as e:
                    logger.warning(f"AI engine failed, falling back to contextual response: {e}")

            # Fallback to contextual response based on available data
            response = await self._create_contextual_response(context)
            return response

        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            return "❌ Sorry, I encountered an error generating a response. Please try again."

    async def _use_ai_engine(self, prompt: str, context: Dict[str, Any]) -> str:
        """Use the AI engine to generate a response."""
        try:
            # Prepare the message for the AI engine
            whatsapp_data = context.get("whatsapp_data", {})
            current_message = context.get("current_message", "")

            # Create a system message with context
            system_message = (
                "You are a helpful AI assistant for WhatsApp message management. "
                "You have access to the user's WhatsApp message history and can help with:\n"
                "- Finding specific messages or conversations\n"
                "- Providing summaries of conversations\n"
                "- Answering questions about contacts and message patterns\n"
                "- General assistance with message management\n\n"
                "Be conversational, helpful, and provide specific information when available."
            )

            # Add context information to the prompt
            context_info = []

            if whatsapp_data.get("message_stats"):
                stats = whatsapp_data["message_stats"]
                context_info.append(f"Message Statistics: {stats['total_messages']} total messages, {stats['messages_today']} today, {stats['unique_contacts']} contacts")

            if whatsapp_data.get("recent_messages"):
                recent_count = len(whatsapp_data["recent_messages"])
                context_info.append(f"Recent Messages: {recent_count} recent messages available")

            if whatsapp_data.get("active_conversations"):
                active_count = len(whatsapp_data["active_conversations"])
                context_info.append(f"Active Conversations: {active_count} active conversations")

            if whatsapp_data.get("mentioned_contacts"):
                contacts = [c["name"] for c in whatsapp_data["mentioned_contacts"]]
                context_info.append(f"Mentioned Contacts: {', '.join(contacts)}")

            # Build the full prompt
            full_prompt = prompt
            if context_info:
                full_prompt += f"\n\nAvailable Context:\n" + "\n".join(f"- {info}" for info in context_info)

            # Use the AI engine's generate_response method
            if hasattr(self.ai_engine, 'generate_response'):
                response = await self.ai_engine.generate_response(
                    message=current_message,
                    system_message=system_message,
                    context=full_prompt
                )
                return response
            elif hasattr(self.ai_engine, 'process_message'):
                # Alternative method name
                response = await self.ai_engine.process_message(
                    message=current_message,
                    context=full_prompt
                )
                return response
            else:
                logger.warning("AI engine doesn't have expected methods")
                return None

        except Exception as e:
            logger.error(f"Error using AI engine: {e}")
            return None

    async def _build_ai_prompt(self, context: Dict[str, Any]) -> str:
        """Build a comprehensive prompt for the AI."""
        prompt_parts = [
            "You are a helpful AI assistant for WhatsApp message management.",
            "You have access to the user's WhatsApp message history and can help with:",
            "- Finding specific messages or conversations",
            "- Providing summaries of conversations",
            "- Answering questions about contacts and message patterns",
            "- Providing statistics and insights about messaging activity",
            "- General assistance with message management",
            "",
            f"User's current question/request: {context['current_message']}",
            ""
        ]

        # Add conversation history for context
        if context.get("conversation_history"):
            prompt_parts.append("Previous conversation context:")
            for msg in context["conversation_history"][-3:]:  # Last 3 messages for context
                role = "User" if msg['role'] == 'user' else "Assistant"
                prompt_parts.append(f"{role}: {msg['content'][:100]}...")
            prompt_parts.append("")

        # Add detailed WhatsApp data context
        whatsapp_data = context.get("whatsapp_data", {})
        if whatsapp_data:
            prompt_parts.append("Available WhatsApp data:")

            # Message statistics
            if whatsapp_data.get("message_stats"):
                stats = whatsapp_data["message_stats"]
                prompt_parts.append(f"- Total messages: {stats.get('total_messages', 0):,}")
                prompt_parts.append(f"- Messages today: {stats.get('messages_today', 0):,}")
                prompt_parts.append(f"- Messages this week: {stats.get('messages_this_week', 0):,}")
                prompt_parts.append(f"- Unique contacts: {stats.get('unique_contacts', 0):,}")

            # Recent messages
            if whatsapp_data.get("recent_messages"):
                recent = whatsapp_data["recent_messages"]
                prompt_parts.append(f"- Recent messages ({len(recent)} available):")
                for msg in recent[:3]:  # Show first 3
                    sender = "You" if msg["is_from_me"] else msg["contact_name"]
                    prompt_parts.append(f"  • {sender}: {msg['message_text'][:50]}...")

            # Active conversations
            if whatsapp_data.get("active_conversations"):
                active = whatsapp_data["active_conversations"]
                prompt_parts.append(f"- Active conversations ({len(active)} total):")
                for conv in active[:3]:  # Show top 3
                    prompt_parts.append(f"  • {conv['contact_name']}: {conv['message_count']} messages")

            # Mentioned contacts with their data
            if whatsapp_data.get("mentioned_contacts"):
                contacts = whatsapp_data["mentioned_contacts"]
                prompt_parts.append(f"- Mentioned contacts ({len(contacts)}):")
                for contact in contacts:
                    prompt_parts.append(f"  • {contact['name']} ({contact['phone_number']})")

                # Add conversation data for mentioned contacts
                if whatsapp_data.get("contact_conversations"):
                    conversations = whatsapp_data["contact_conversations"]
                    prompt_parts.append("- Recent conversations with mentioned contacts:")
                    for contact_name, messages in conversations.items():
                        prompt_parts.append(f"  • {contact_name} ({len(messages)} recent messages):")
                        for msg in messages[:2]:  # Show 2 most recent
                            sender = "You" if msg["is_from_me"] else contact_name
                            prompt_parts.append(f"    - {sender}: {msg['message_text'][:40]}...")

            # Search results
            if whatsapp_data.get("relevant_messages"):
                results = whatsapp_data["relevant_messages"]
                prompt_parts.append(f"- Search results ({len(results)} found):")
                for result in results:
                    sender = "You" if result["is_from_me"] else result["contact_name"]
                    prompt_parts.append(f"  • {sender}: {result['message_text'][:60]}...")

        prompt_parts.extend([
            "",
            "Please provide a helpful, conversational response based on the available data.",
            "If specific data is requested but not available, explain what data you do have access to.",
            "Use emojis and formatting to make responses engaging and easy to read."
        ])

        return "\n".join(prompt_parts)
    
    async def _create_contextual_response(self, context: Dict[str, Any]) -> str:
        """Create a contextual response based on the available data."""
        message = context["current_message"].lower()
        whatsapp_data = context.get("whatsapp_data", {})
        
        # Handle specific types of queries
        if any(word in message for word in ["status", "stats", "statistics"]):
            return await self._create_stats_response(whatsapp_data)
        
        elif any(word in message for word in ["contacts", "who", "people"]):
            return await self._create_contacts_response(whatsapp_data)
        
        elif any(word in message for word in ["recent", "latest", "last"]):
            return await self._create_recent_messages_response(whatsapp_data)
        
        elif any(word in message for word in ["search", "find", "show"]):
            return await self._create_search_response(whatsapp_data)
        
        elif whatsapp_data.get("mentioned_contacts"):
            return await self._create_contact_specific_response(whatsapp_data)
        
        else:
            return await self._create_general_response(context)
    
    async def _create_stats_response(self, whatsapp_data: Dict[str, Any]) -> str:
        """Create a response with statistics."""
        stats = whatsapp_data.get("message_stats", {})
        
        response = "📊 **WhatsApp Statistics**\n\n"
        response += f"📱 Total Messages: {stats.get('total_messages', 0):,}\n"
        response += f"📅 Messages Today: {stats.get('messages_today', 0):,}\n"
        response += f"📈 Messages This Week: {stats.get('messages_this_week', 0):,}\n"
        response += f"👥 Unique Contacts: {stats.get('unique_contacts', 0):,}\n"
        
        active_conversations = whatsapp_data.get("active_conversations", [])
        if active_conversations:
            response += f"\n🔥 **Most Active Conversations:**\n"
            for conv in active_conversations[:5]:
                response += f"• {conv['contact_name']}: {conv['message_count']} messages\n"
        
        return response
    
    async def _create_contacts_response(self, whatsapp_data: Dict[str, Any]) -> str:
        """Create a response about contacts."""
        total_contacts = whatsapp_data.get("total_contacts", 0)
        active_conversations = whatsapp_data.get("active_conversations", [])
        
        response = f"👥 **Contacts Information**\n\n"
        response += f"Total Contacts: {total_contacts}\n\n"
        
        if active_conversations:
            response += "🔥 **Recently Active Contacts:**\n"
            for conv in active_conversations[:10]:
                response += f"• {conv['contact_name']} ({conv['message_count']} messages)\n"
        
        return response
    
    async def _create_recent_messages_response(self, whatsapp_data: Dict[str, Any]) -> str:
        """Create a response with recent messages."""
        recent_messages = whatsapp_data.get("recent_messages", [])
        
        if not recent_messages:
            return "📭 No recent messages found."
        
        response = "📱 **Recent Messages:**\n\n"
        for msg in recent_messages:
            sender = "You" if msg["is_from_me"] else msg["contact_name"]
            response += f"💬 **{sender}**: {msg['message_text']}\n"
            if msg["timestamp"]:
                response += f"   ⏰ {msg['timestamp'][:19]}\n"
            response += "\n"
        
        return response
    
    async def _create_search_response(self, whatsapp_data: Dict[str, Any]) -> str:
        """Create a response with search results."""
        search_results = whatsapp_data.get("relevant_messages", [])
        
        if not search_results:
            return "🔍 No matching messages found for your search."
        
        response = "🔍 **Search Results:**\n\n"
        for result in search_results:
            sender = "You" if result["is_from_me"] else result["contact_name"]
            response += f"💬 **{sender}**: {result['message_text']}\n"
            if result["timestamp"]:
                response += f"   ⏰ {result['timestamp'][:19]}\n"
            response += "\n"
        
        return response
    
    async def _create_contact_specific_response(self, whatsapp_data: Dict[str, Any]) -> str:
        """Create a response about specific contacts."""
        mentioned_contacts = whatsapp_data.get("mentioned_contacts", [])
        contact_conversations = whatsapp_data.get("contact_conversations", {})
        
        if not mentioned_contacts:
            return "👤 No specific contacts found in your message."
        
        response = "👤 **Contact Information:**\n\n"
        
        for contact in mentioned_contacts:
            response += f"**{contact['name']}**\n"
            response += f"📞 {contact['phone_number']}\n"
            
            if contact['name'] in contact_conversations:
                messages = contact_conversations[contact['name']]
                response += f"💬 Recent messages ({len(messages)}):\n"
                
                for msg in messages[:3]:  # Show last 3 messages
                    sender = "You" if msg["is_from_me"] else contact['name']
                    response += f"  • {sender}: {msg['message_text'][:100]}...\n"
                
            response += "\n"
        
        return response
    
    async def _create_general_response(self, context: Dict[str, Any]) -> str:
        """Create a general helpful response."""
        return (
            "👋 Hi! I'm your WhatsApp AI Assistant. I can help you with:\n\n"
            "📊 **Statistics**: Ask about message stats, contact counts, etc.\n"
            "👥 **Contacts**: Get information about your contacts\n"
            "🔍 **Search**: Find specific messages or conversations\n"
            "📱 **Recent Activity**: See your latest messages\n\n"
            "Try asking me things like:\n"
            "• 'Show me recent messages'\n"
            "• 'What are my message statistics?'\n"
            "• 'Find messages about [topic]'\n"
            "• 'Tell me about [contact name]'\n\n"
            "What would you like to know?"
        )
    
    async def _memory_cleanup_task(self):
        """Periodic cleanup of old conversation memory."""
        while True:
            try:
                await asyncio.sleep(self.memory_cleanup_interval)
                
                current_time = datetime.now()
                cutoff_time = current_time - timedelta(hours=24)  # Keep 24 hours of memory
                
                for user_id in list(self.conversation_memory.keys()):
                    user_memory = self.conversation_memory[user_id]
                    
                    # Filter out old messages
                    filtered_memory = []
                    for msg in user_memory:
                        msg_time = datetime.fromisoformat(msg["timestamp"])
                        if msg_time > cutoff_time:
                            filtered_memory.append(msg)
                    
                    if filtered_memory:
                        self.conversation_memory[user_id] = filtered_memory
                    else:
                        del self.conversation_memory[user_id]
                
                logger.debug("Conversation memory cleanup completed")
                
            except Exception as e:
                logger.error(f"Error in memory cleanup task: {e}")
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """Get statistics about active conversations."""
        return {
            "active_conversations": len(self.conversation_memory),
            "total_memory_messages": sum(len(msgs) for msgs in self.conversation_memory.values()),
            "cache_size": len(self.contacts_cache),
            "last_cache_update": self.last_cache_update.isoformat() if self.last_cache_update else None
        }
